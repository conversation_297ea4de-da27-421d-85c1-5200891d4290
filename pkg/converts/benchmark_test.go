package converts

import (
	"reflect"
	"strconv"
	"testing"
)

// ================================ 基准测试 ================================

func BenchmarkOriginalIntToString(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = To[string](42)
	}
}

func BenchmarkOptimizedIntToString(b *testing.B) {
	converter := NewOptimizedConverter()
	opts := getOptions(nil)
	defer putOptions(opts)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		src, _, info := indirectOptimized(42)
		_, _ = converter.Convert(src, info, stringType, opts)
	}
}

func BenchmarkStandardIntToString(b *testing.B) {
	b.<PERSON>setTimer()
	for i := 0; i < b.N; i++ {
		_ = strconv.Itoa(42)
	}
}

func BenchmarkOriginalStringToInt(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = To[int]("42")
	}
}

func BenchmarkOptimizedStringToInt(b *testing.B) {
	converter := NewOptimizedConverter()
	opts := getOptions(nil)
	defer putOptions(opts)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		src, _, info := indirectOptimized("42")
		_, _ = converter.Convert(src, info, intType, opts)
	}
}

func BenchmarkStandardStringToInt(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = strconv.Atoi("42")
	}
}

func BenchmarkOriginalBoolToInt(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = To[int](true)
	}
}

func BenchmarkOptimizedBoolToInt(b *testing.B) {
	converter := NewOptimizedConverter()
	opts := getOptions(nil)
	defer putOptions(opts)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		src, _, info := indirectOptimized(true)
		_, _ = converter.Convert(src, info, intType, opts)
	}
}

// ================================ 内存分配测试 ================================

func BenchmarkOriginalMemoryAlloc(b *testing.B) {
	b.ReportAllocs()
	for i := 0; i < b.N; i++ {
		_, _ = To[string](42)
		_, _ = To[int]("42")
		_, _ = To[bool](1)
	}
}

func BenchmarkOptimizedMemoryAlloc(b *testing.B) {
	converter := NewOptimizedConverter()

	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		opts := getOptions(nil)

		src1, _, info1 := indirectOptimized(42)
		_, _ = converter.Convert(src1, info1, stringType, opts)

		src2, _, info2 := indirectOptimized("42")
		_, _ = converter.Convert(src2, info2, intType, opts)

		src3, _, info3 := indirectOptimized(1)
		_, _ = converter.Convert(src3, info3, boolType, opts)

		putOptions(opts)
	}
}

// ================================ 复杂场景测试 ================================

func BenchmarkOriginalComplexConversion(b *testing.B) {
	testCases := []any{
		42, "42", true, 3.14, "3.14", false,
		int8(8), uint16(16), float32(32.0), "true",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, tc := range testCases {
			_, _ = To[string](tc)
		}
	}
}

func BenchmarkOptimizedComplexConversion(b *testing.B) {
	converter := NewOptimizedConverter()
	testCases := []any{
		42, "42", true, 3.14, "3.14", false,
		int8(8), uint16(16), float32(32.0), "true",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		opts := getOptions(nil)
		for _, tc := range testCases {
			src, _, info := indirectOptimized(tc)
			_, _ = converter.Convert(src, info, stringType, opts)
		}
		putOptions(opts)
	}
}

// ================================ 并发测试 ================================

func BenchmarkOriginalConcurrent(b *testing.B) {
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, _ = To[string](42)
			_, _ = To[int]("42")
			_, _ = To[bool](1)
		}
	})
}

func BenchmarkOptimizedConcurrent(b *testing.B) {
	converter := NewOptimizedConverter()

	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			opts := getOptions(nil)

			src1, _, info1 := indirectOptimized(42)
			_, _ = converter.Convert(src1, info1, stringType, opts)

			src2, _, info2 := indirectOptimized("42")
			_, _ = converter.Convert(src2, info2, intType, opts)

			src3, _, info3 := indirectOptimized(1)
			_, _ = converter.Convert(src3, info3, boolType, opts)

			putOptions(opts)
		}
	})
}

// ================================ 字符串转换性能测试 ================================

func BenchmarkOriginalBytesToString(b *testing.B) {
	data := []byte("hello world")
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = To[string](data)
	}
}

func BenchmarkOptimizedBytesToString(b *testing.B) {
	data := []byte("hello world")
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = bytesToString(data)
	}
}

func BenchmarkStandardBytesToString(b *testing.B) {
	data := []byte("hello world")
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = string(data)
	}
}

func BenchmarkOriginalStringToBytes(b *testing.B) {
	data := "hello world"
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = To[[]byte](data)
	}
}

func BenchmarkOptimizedStringToBytes(b *testing.B) {
	data := "hello world"
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = stringToBytes(data)
	}
}

func BenchmarkStandardStringToBytes(b *testing.B) {
	data := "hello world"
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = []byte(data)
	}
}

// ================================ 测试辅助 ================================

var (
	stringType = getStringType()
	intType    = getIntType()
	boolType   = getBoolType()
)

func getStringType() reflect.Type {
	return reflect.TypeOf("")
}

func getIntType() reflect.Type {
	return reflect.TypeOf(0)
}

func getBoolType() reflect.Type {
	return reflect.TypeOf(false)
}

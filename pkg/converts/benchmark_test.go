package converts

import (
	"strconv"
	"testing"
)

// ================================ 基准测试 ================================

// 测试原始版本的性能
func BenchmarkOriginalIntToString(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = To[string](42)
	}
}

func BenchmarkOriginalStringToInt(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = To[int]("42")
	}
}

func BenchmarkOriginalBoolToInt(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = To[int](true)
	}
}

func BenchmarkOriginalBoolToString(b *testing.B) {
	b.<PERSON>set<PERSON>ime<PERSON>()
	for i := 0; i < b.N; i++ {
		_, _ = To[string](true)
	}
}

func BenchmarkOriginalFloatToString(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = To[string](3.14)
	}
}

func BenchmarkOriginalStringToBool(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = To[bool]("true")
	}
}

// 测试标准库的性能作为对比
func BenchmarkStandardIntToString(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = strconv.Itoa(42)
	}
}

func BenchmarkStandardStringToInt(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = strconv.Atoi("42")
	}
}

func BenchmarkStandardFloatToString(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = strconv.FormatFloat(3.14, 'f', -1, 64)
	}
}

func BenchmarkStandardStringToBool(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = strconv.ParseBool("true")
	}
}

// ================================ 内存分配测试 ================================

func BenchmarkOriginalMemoryAlloc(b *testing.B) {
	b.ReportAllocs()
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = To[string](42)
		_, _ = To[int]("42")
		_, _ = To[bool](1)
		_, _ = To[string](true)
	}
}

// ================================ 复杂场景测试 ================================

func BenchmarkOriginalComplexConversion(b *testing.B) {
	testCases := []any{
		42, "42", true, 3.14, "3.14", false,
		int8(8), uint16(16), float32(32.0), "true",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, tc := range testCases {
			_, _ = To[string](tc)
		}
	}
}

// ================================ 并发测试 ================================

func BenchmarkOriginalConcurrent(b *testing.B) {
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, _ = To[string](42)
			_, _ = To[int]("42")
			_, _ = To[bool](1)
		}
	})
}

// ================================ 字符串转换性能测试 ================================

func BenchmarkOriginalBytesToString(b *testing.B) {
	data := []byte("hello world")
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = To[string](data)
	}
}

func BenchmarkStandardBytesToString(b *testing.B) {
	data := []byte("hello world")
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = string(data)
	}
}

func BenchmarkOriginalStringToBytes(b *testing.B) {
	data := "hello world"
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = To[[]byte](data)
	}
}

func BenchmarkStandardStringToBytes(b *testing.B) {
	data := "hello world"
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = []byte(data)
	}
}

// ================================ 错误处理性能测试 ================================

func BenchmarkOriginalErrorHandling(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = To[int]("invalid") // 这会产生错误
	}
}

func BenchmarkStandardErrorHandling(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = strconv.Atoi("invalid") // 这会产生错误
	}
}

// ================================ Try 和 Must 函数测试 ================================

func BenchmarkTryFunction(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = Try[int]("42", 0)
	}
}

func BenchmarkTryFunctionWithDefault(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = Try[int]("invalid", 999) // 使用默认值
	}
}

func BenchmarkMustFunction(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = Must[string](42) // 使用一个肯定成功的转换
	}
}

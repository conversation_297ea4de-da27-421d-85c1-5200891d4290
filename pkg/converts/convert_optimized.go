package converts

import (
	"fmt"
	"reflect"
	"sync"
	"unsafe"
)

// ================================ 优化后的错误处理 ================================

type ConvertError struct {
	From   string
	To     string
	Value  any
	Reason string
}

func (e *ConvertError) Error() string {
	return fmt.Sprintf("convert %s to %s failed: %s (value: %v)",
		e.From, e.To, e.Reason, e.Value)
}

func newConvertError(from, to string, value any, reason string) *ConvertError {
	return &ConvertError{From: from, To: to, Value: value, Reason: reason}
}

// ================================ 类型信息缓存 ================================

type TypeInfo struct {
	Kind     reflect.Kind
	Size     int
	IsInt    bool
	IsUint   bool
	IsFloat  bool
	IsBool   bool
	IsString bool
	IsBytes  bool
	IsRunes  bool
}

var (
	typeInfoCache = sync.Map{}
	commonTypes   = map[reflect.Type]*TypeInfo{}
)

func init() {
	// 预缓存常用类型
	cacheCommonTypes()
}

func cacheCommonTypes() {
	types := []reflect.Type{
		reflect.TypeOf(bool(false)),
		reflect.TypeOf(int(0)),
		reflect.TypeOf(int8(0)),
		reflect.TypeOf(int16(0)),
		reflect.TypeOf(int32(0)),
		reflect.TypeOf(int64(0)),
		reflect.TypeOf(uint(0)),
		reflect.TypeOf(uint8(0)),
		reflect.TypeOf(uint16(0)),
		reflect.TypeOf(uint32(0)),
		reflect.TypeOf(uint64(0)),
		reflect.TypeOf(uintptr(0)),
		reflect.TypeOf(float32(0)),
		reflect.TypeOf(float64(0)),
		reflect.TypeOf(complex64(0)),
		reflect.TypeOf(complex128(0)),
		reflect.TypeOf(""),
		reflect.TypeOf([]byte{}),
		reflect.TypeOf([]rune{}),
	}

	for _, t := range types {
		info := buildTypeInfo(t)
		commonTypes[t] = info
	}
}

func getTypeInfo(t reflect.Type) *TypeInfo {
	if info, ok := commonTypes[t]; ok {
		return info
	}

	if cached, ok := typeInfoCache.Load(t); ok {
		return cached.(*TypeInfo)
	}

	info := buildTypeInfo(t)
	typeInfoCache.Store(t, info)
	return info
}

func buildTypeInfo(t reflect.Type) *TypeInfo {
	info := &TypeInfo{
		Kind: t.Kind(),
		Size: int(t.Size()),
	}

	switch t.Kind() {
	case reflect.Bool:
		info.IsBool = true
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		info.IsInt = true
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		info.IsUint = true
	case reflect.Float32, reflect.Float64:
		info.IsFloat = true
	case reflect.String:
		info.IsString = true
	case reflect.Slice:
		if t.Elem().Kind() == reflect.Uint8 {
			info.IsBytes = true
		} else if t.Elem().Kind() == reflect.Int32 {
			info.IsRunes = true
		}
	}

	return info
}

// ================================ 快速转换路径 ================================

// FastConverter 定义快速转换函数类型
type FastConverter func(src any, dst unsafe.Pointer) error

var fastConverters = map[string]FastConverter{}

func registerFastConverter(fromType, toType reflect.Type, converter FastConverter) {
	key := fmt.Sprintf("%s->%s", fromType.String(), toType.String())
	fastConverters[key] = converter
}

func getFastConverter(fromType, toType reflect.Type) FastConverter {
	key := fmt.Sprintf("%s->%s", fromType.String(), toType.String())
	return fastConverters[key]
}

// ================================ 优化的选项处理 ================================

var defaultOptionsPool = sync.Pool{
	New: func() interface{} {
		return &Options{
			Boolean: &BooleanOptions{},
			Numeric: &NumericOptions{},
			Complex: &ComplexOptions{},
			Textual: &TextualOptions{},
		}
	},
}

func getOptions(opts []Option) *Options {
	if len(opts) == 0 {
		// 无选项时直接返回默认值，避免内存分配
		return &Options{
			Boolean: &BooleanOptions{},
			Numeric: &NumericOptions{},
			Complex: &ComplexOptions{},
			Textual: &TextualOptions{},
		}
	}

	opt := defaultOptionsPool.Get().(*Options)
	// 重置选项
	*opt.Boolean = BooleanOptions{}
	*opt.Numeric = NumericOptions{}
	*opt.Complex = ComplexOptions{}
	*opt.Textual = TextualOptions{}

	for _, o := range opts {
		o(opt)
	}

	return opt
}

func putOptions(opt *Options) {
	if opt != nil {
		defaultOptionsPool.Put(opt)
	}
}

// ================================ 零拷贝字符串转换 ================================

// bytesToString 零拷贝转换 []byte 到 string
func bytesToString(b []byte) string {
	if len(b) == 0 {
		return ""
	}
	return *(*string)(unsafe.Pointer(&b))
}

// stringToBytes 零拷贝转换 string 到 []byte
func stringToBytes(s string) []byte {
	if len(s) == 0 {
		return nil
	}
	return *(*[]byte)(unsafe.Pointer(&struct {
		string
		int
	}{s, len(s)}))
}

// runesToString 高效转换 []rune 到 string
func runesToString(r []rune) string {
	if len(r) == 0 {
		return ""
	}
	return string(r) // 这里仍需要拷贝，但比多次转换效率高
}

// ================================ 优化的间接解引用 ================================

func indirectOptimized(i any) (any, bool, *TypeInfo) {
	if i == nil {
		return nil, false, nil
	}

	// 快速路径：检查是否为常见类型
	t := reflect.TypeOf(i)
	if info, ok := commonTypes[t]; ok {
		return i, false, info
	}

	v := reflect.ValueOf(i)
	hasPtr := false

	for v.Kind() == reflect.Ptr || v.Kind() == reflect.Interface {
		if v.IsNil() {
			return nil, hasPtr, nil
		}
		hasPtr = true
		v = v.Elem()
		t = v.Type()

		// 检查解引用后是否为常见类型
		if info, ok := commonTypes[t]; ok {
			return v.Interface(), hasPtr, info
		}
	}

	info := getTypeInfo(t)
	return v.Interface(), hasPtr, info
}

package converts

import (
	"reflect"
	"strconv"
)

type (
	Complex interface {
		~complex64 | ~complex128
	}

	complex_t[T Complex] struct{ val T }

	ComplexOptions struct{}
)

func (t complex_t[T]) bitSize() int {
	if _, ok := any(t.val).(complex64); ok {
		return 64
	}
	return 128
}

// ================================ 泛型转换函数 ================================

func boolean2complex[S Boolean, D Complex](src S) (D, error) {
	if bool(src) {
		return D(1 + 0i), nil
	}
	return D(0 + 0i), nil
}

func numeric2complex[S Numeric, D Complex](src S) (D, error) {
	return D(complex(float64(src), 0)), nil
}

func complex2complex[S Complex, D Complex](src S) (D, error) {
	return D(src), nil
}

func textual2complex[S Textual, D Complex](src S) (D, error) {
	dst, err := strconv.ParseComplex(textual_t[S]{src}.toString(), complex_t[D]{}.bitSize())
	return D(dst), err
}

func ToComplex[D Complex](src any, opt *ComplexOptions) (D, error) {
	switch s := any(src).(type) {
	case bool:
		return boolean2complex[bool, D](s)
	case int:
		return numeric2complex[int, D](s)
	case int8:
		return numeric2complex[int8, D](s)
	case int16:
		return numeric2complex[int16, D](s)
	case int32:
		return numeric2complex[int32, D](s)
	case int64:
		return numeric2complex[int64, D](s)
	case uint:
		return numeric2complex[uint, D](s)
	case uint8:
		return numeric2complex[uint8, D](s)
	case uint16:
		return numeric2complex[uint16, D](s)
	case uint32:
		return numeric2complex[uint32, D](s)
	case uint64:
		return numeric2complex[uint64, D](s)
	case uintptr:
		return numeric2complex[uintptr, D](s)
	case float32:
		return numeric2complex[float32, D](s)
	case float64:
		return numeric2complex[float64, D](s)
	case complex64:
		return complex2complex[complex64, D](s)
	case complex128:
		return complex2complex[complex128, D](s)
	case string:
		return textual2complex[string, D](s)
	case []byte:
		return textual2complex[[]byte, D](s)
	case []rune:
		return textual2complex[[]rune, D](s)
	default:
		v := reflect.ValueOf(src)
		switch {
		case v.Kind() == reflect.Bool:
			return boolean2complex[bool, D](v.Bool())
		case v.CanInt():
			return numeric2complex[int64, D](v.Int())
		case v.CanUint():
			return numeric2complex[uint64, D](v.Uint())
		case v.CanFloat():
			return numeric2complex[float64, D](v.Float())
		case v.CanComplex():
			return complex2complex[complex128, D](v.Complex())
		case v.Kind() == reflect.String:
			return textual2complex[string, D](v.String())
		case v.Kind() == reflect.Slice:
			if v.Type().Elem().Kind() == reflect.Uint8 {
				return textual2complex[[]byte, D](v.Bytes())
			} else if v.Type().Elem().Kind() == reflect.Int32 {
				return textual2complex[[]rune, D](v.Interface().([]rune))
			}
		}
		return D(0), ErrUnsupported
	}
}

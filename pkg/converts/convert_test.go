package converts

import (
	"math"
	"reflect"
	"testing"
)

// ================================ 基础转换测试 ================================

func TestBasicConversions(t *testing.T) {
	tests := []struct {
		name     string
		src      any
		expected any
		wantErr  bool
	}{
		// Boolean conversions
		{"bool to int", true, 1, false},
		{"bool to string", false, "false", false},
		{"int to bool", 0, false, false},
		{"int to bool non-zero", 42, true, false},
		
		// Numeric conversions
		{"int to float", 42, 42.0, false},
		{"float to int", 42.7, 42, false},
		{"string to int", "123", 123, false},
		{"invalid string to int", "abc", 0, true},
		
		// String conversions
		{"int to string", 42, "42", false},
		{"float to string", 3.14, "3.14", false},
		{"bool to string", true, "true", false},
		
		// Complex conversions
		{"int to complex", 42, complex(42, 0), false},
		{"complex to string", complex(1, 2), "(1+2i)", false},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := convertGeneric(tt.src, reflect.TypeOf(tt.expected))
			
			if tt.wantErr {
				if err == nil {
					t.Errorf("expected error but got none")
				}
				return
			}
			
			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}
			
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("expected %v (%T), got %v (%T)", tt.expected, tt.expected, result, result)
			}
		})
	}
}

// ================================ 边界值测试 ================================

func TestBoundaryValues(t *testing.T) {
	tests := []struct {
		name    string
		src     any
		dstType reflect.Type
		wantErr bool
	}{
		// Integer overflow tests
		{"int64 max to int32", math.MaxInt64, reflect.TypeOf(int32(0)), true},
		{"int32 max to int32", math.MaxInt32, reflect.TypeOf(int32(0)), false},
		{"negative to uint", -1, reflect.TypeOf(uint(0)), true},
		
		// Float precision tests
		{"float64 to float32 overflow", math.MaxFloat64, reflect.TypeOf(float32(0)), true},
		{"float32 max to float32", math.MaxFloat32, reflect.TypeOf(float32(0)), false},
		
		// String parsing edge cases
		{"empty string to int", "", reflect.TypeOf(0), true},
		{"whitespace string to int", "  ", reflect.TypeOf(0), true},
		{"scientific notation", "1e10", reflect.TypeOf(float64(0)), false},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := convertGeneric(tt.src, tt.dstType)
			
			if tt.wantErr && err == nil {
				t.Errorf("expected error but got none")
			}
			if !tt.wantErr && err != nil {
				t.Errorf("unexpected error: %v", err)
			}
		})
	}
}

// ================================ 选项测试 ================================

func TestBooleanOptions(t *testing.T) {
	opts := &BooleanOptions{
		TrueSet:  map[string]struct{}{"yes": {}, "on": {}, "1": {}},
		FalseSet: map[string]struct{}{"no": {}, "off": {}, "0": {}},
	}
	
	tests := []struct {
		input    string
		expected bool
	}{
		{"yes", true},
		{"YES", true},
		{"on", true},
		{"1", true},
		{"no", false},
		{"NO", false},
		{"off", false},
		{"0", false},
		{"true", true},  // fallback to standard parsing
		{"false", false}, // fallback to standard parsing
	}
	
	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result, err := ToBoolean[bool](tt.input, opts)
			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}
			if result != tt.expected {
				t.Errorf("expected %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestNumericStrictMode(t *testing.T) {
	strictOpts := &NumericOptions{Strict: true}
	normalOpts := &NumericOptions{Strict: false}
	
	tests := []struct {
		name       string
		src        any
		strictErr  bool
		normalErr  bool
	}{
		{"int64 max to int32", math.MaxInt64, true, false},
		{"valid conversion", 100, false, false},
		{"float to int precision loss", 3.14, true, false},
	}
	
	for _, tt := range tests {
		t.Run(tt.name+" strict", func(t *testing.T) {
			_, err := ToNumeric[int32](tt.src, strictOpts)
			if tt.strictErr && err == nil {
				t.Error("expected error in strict mode")
			}
			if !tt.strictErr && err != nil {
				t.Errorf("unexpected error in strict mode: %v", err)
			}
		})
		
		t.Run(tt.name+" normal", func(t *testing.T) {
			_, err := ToNumeric[int32](tt.src, normalOpts)
			if tt.normalErr && err == nil {
				t.Error("expected error in normal mode")
			}
			if !tt.normalErr && err != nil {
				t.Errorf("unexpected error in normal mode: %v", err)
			}
		})
	}
}

// ================================ 泛型API测试 ================================

func TestGenericAPI(t *testing.T) {
	// Test To function
	result, err := To[int]("42")
	if err != nil {
		t.Errorf("To function failed: %v", err)
	}
	if result != 42 {
		t.Errorf("expected 42, got %v", result)
	}
	
	// Test Try function
	result2 := Try[int]("invalid", 999)
	if result2 != 999 {
		t.Errorf("Try function should return default value, got %v", result2)
	}
	
	result3 := Try[int]("42", 999)
	if result3 != 42 {
		t.Errorf("Try function should return converted value, got %v", result3)
	}
	
	// Test Must function (success case)
	result4 := Must[int]("42")
	if result4 != 42 {
		t.Errorf("Must function failed, got %v", result4)
	}
	
	// Test Must function (panic case)
	defer func() {
		if r := recover(); r == nil {
			t.Error("Must function should panic on invalid input")
		}
	}()
	Must[int]("invalid")
}

// ================================ 指针处理测试 ================================

func TestPointerHandling(t *testing.T) {
	// Test single pointer
	val := 42
	ptr := &val
	result, err := To[string](ptr)
	if err != nil {
		t.Errorf("pointer conversion failed: %v", err)
	}
	if result != "42" {
		t.Errorf("expected '42', got %v", result)
	}
	
	// Test double pointer
	ptrPtr := &ptr
	result2, err := To[string](ptrPtr)
	if err != nil {
		t.Errorf("double pointer conversion failed: %v", err)
	}
	if result2 != "42" {
		t.Errorf("expected '42', got %v", result2)
	}
	
	// Test nil pointer
	var nilPtr *int
	_, err = To[string](nilPtr)
	if err == nil {
		t.Error("nil pointer should return error")
	}
}

// ================================ 并发安全测试 ================================

func TestConcurrentSafety(t *testing.T) {
	const numGoroutines = 100
	const numOperations = 1000
	
	done := make(chan bool, numGoroutines)
	
	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer func() { done <- true }()
			
			for j := 0; j < numOperations; j++ {
				// Test various conversions concurrently
				_, _ = To[string](j)
				_, _ = To[int]("42")
				_, _ = To[bool](j%2 == 0)
				_, _ = To[float64](j)
			}
		}()
	}
	
	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		<-done
	}
}

// ================================ 性能回归测试 ================================

func TestPerformanceRegression(t *testing.T) {
	// Simple performance smoke test
	const iterations = 10000
	
	// Test that basic conversions don't take too long
	for i := 0; i < iterations; i++ {
		_, _ = To[string](i)
		_, _ = To[int]("42")
		_, _ = To[bool](i%2 == 0)
	}
}

// ================================ 辅助函数 ================================

func convertGeneric(src any, dstType reflect.Type) (any, error) {
	// This is a simplified version for testing
	// In practice, you'd use the actual conversion logic
	switch dstType.Kind() {
	case reflect.String:
		return To[string](src)
	case reflect.Int:
		return To[int](src)
	case reflect.Bool:
		return To[bool](src)
	case reflect.Float64:
		return To[float64](src)
	case reflect.Complex128:
		return To[complex128](src)
	case reflect.Int32:
		return To[int32](src)
	case reflect.Uint:
		return To[uint](src)
	case reflect.Float32:
		return To[float32](src)
	default:
		return nil, ErrUnsupported
	}
}

package converts

import (
	"errors"
	"reflect"
)

var (
	ErrOverflow    = errors.New("value overflow")
	ErrUnsupported = errors.New("unsupported type")
)

// indirect 获取值的实际类型
func indirect(i any) (any, bool) {
	if i == nil {
		return nil, false
	}
	v := reflect.ValueOf(i)
	x := false
	for v.Kind() == reflect.Ptr || v.Kind() == reflect.Interface {
		if v.IsNil() {
			return nil, x
		}
		x = true
		v = v.Elem()
	}
	return v.Interface(), x
}

type (
	Option  func(*Options)
	Options struct {
		Boolean *BooleanOptions
		Numeric *NumericOptions
		Complex *ComplexOptions
		Textual *TextualOptions
	}
)

func DefaultOptions() *Options {
	return &Options{
		Boolean: &BooleanOptions{},
		Numeric: &NumericOptions{},
		Complex: &ComplexOptions{},
		Textual: &TextualOptions{},
	}
}

func to(src any, dst any, opts ...Option) error {
	src, ok := indirect(src)
	if !ok {
		return ErrUnsupported
	}
	opt := DefaultOptions()
	for _, o := range opts {
		o(opt)
	}
	var err error
	switch dst := dst.(type) {
	case *bool:
		*dst, err = ToBoolean[bool](src, opt.Boolean)
	case *int:
		*dst, err = ToNumeric[int](src, opt.Numeric)
	case *int8:
		*dst, err = ToNumeric[int8](src, opt.Numeric)
	case *int16:
		*dst, err = ToNumeric[int16](src, opt.Numeric)
	case *int32:
		*dst, err = ToNumeric[int32](src, opt.Numeric)
	case *int64:
		*dst, err = ToNumeric[int64](src, opt.Numeric)
	case *uint:
		*dst, err = ToNumeric[uint](src, opt.Numeric)
	case *uint8:
		*dst, err = ToNumeric[uint8](src, opt.Numeric)
	case *uint16:
		*dst, err = ToNumeric[uint16](src, opt.Numeric)
	case *uint32:
		*dst, err = ToNumeric[uint32](src, opt.Numeric)
	case *uint64:
		*dst, err = ToNumeric[uint64](src, opt.Numeric)
	case *uintptr:
		*dst, err = ToNumeric[uintptr](src, opt.Numeric)
	case *float32:
		*dst, err = ToNumeric[float32](src, opt.Numeric)
	case *float64:
		*dst, err = ToNumeric[float64](src, opt.Numeric)
	case *complex64:
		*dst, err = ToComplex[complex64](src, opt.Complex)
	case *complex128:
		*dst, err = ToComplex[complex128](src, opt.Complex)
	case *string:
		*dst, err = ToTextual[string](src, opt.Textual)
	case *[]byte:
		*dst, err = ToTextual[[]byte](src, opt.Textual)
	case *[]rune:
		*dst, err = ToTextual[[]rune](src, opt.Textual)
	default:
		return ErrUnsupported
	}
	return err
}

// ================================ 通用转换函数 ================================

// To 转换为指定类型
func To[D any](src any, opts ...Option) (D, error) {
	var dst D
	if err := to(src, &dst, opts...); err != nil {
		return dst, err
	}
	return dst, nil
}

// Try 转换为指定类型，如果失败则返回默认值
func Try[D any](src any, def D, opts ...Option) D {
	if dst, err := To[D](src, opts...); err != nil {
		return def
	} else {
		return dst
	}
}

// Must 转换为指定类型，如果失败则panic
func Must[D any](src any, opts ...Option) D {
	if dst, err := To[D](src, opts...); err != nil {
		panic(err)
	} else {
		return dst
	}
}

package converts

import (
	"reflect"
	"strconv"
	"strings"
)

// ================================ 统一的转换器接口 ================================

type Converter interface {
	Convert(src any, srcInfo *TypeInfo, dstType reflect.Type, opts *Options) (any, error)
}

// ================================ 高性能转换器实现 ================================

type OptimizedConverter struct {
	converterMap map[string]func(src any, srcInfo *TypeInfo, dstType reflect.Type, opts *Options) (any, error)
}

func NewOptimizedConverter() *OptimizedConverter {
	c := &OptimizedConverter{
		converterMap: make(map[string]func(src any, srcInfo *TypeInfo, dstType reflect.Type, opts *Options) (any, error)),
	}
	c.registerConverters()
	return c
}

func (c *OptimizedConverter) registerConverters() {
	// 注册所有转换函数
	c.converterMap["bool->int"] = c.boolToInt
	c.converterMap["bool->string"] = c.boolToString
	c.converterMap["int->bool"] = c.intToBool
	c.converterMap["int->string"] = c.intToString
	c.converterMap["string->int"] = c.stringToInt
	c.converterMap["string->bool"] = c.stringToBool
	// ... 更多转换器
}

func (c *OptimizedConverter) Convert(src any, srcInfo *TypeInfo, dstType reflect.Type, opts *Options) (any, error) {
	srcTypeName := getTypeName(srcInfo)
	dstTypeName := dstType.Name()

	key := srcTypeName + "->" + dstTypeName
	if converter, ok := c.converterMap[key]; ok {
		return converter(src, srcInfo, dstType, opts)
	}

	// 回退到通用转换逻辑
	return c.genericConvert(src, srcInfo, dstType, opts)
}

// ================================ 具体转换实现 ================================

func (c *OptimizedConverter) boolToInt(src any, srcInfo *TypeInfo, dstType reflect.Type, opts *Options) (any, error) {
	b := src.(bool)
	if b {
		return reflect.ValueOf(1).Convert(dstType).Interface(), nil
	}
	return reflect.ValueOf(0).Convert(dstType).Interface(), nil
}

func (c *OptimizedConverter) boolToString(src any, srcInfo *TypeInfo, dstType reflect.Type, opts *Options) (any, error) {
	b := src.(bool)
	if b {
		return "true", nil
	}
	return "false", nil
}

func (c *OptimizedConverter) intToBool(src any, srcInfo *TypeInfo, dstType reflect.Type, opts *Options) (any, error) {
	v := reflect.ValueOf(src)
	return v.Int() != 0, nil
}

func (c *OptimizedConverter) intToString(src any, srcInfo *TypeInfo, dstType reflect.Type, opts *Options) (any, error) {
	v := reflect.ValueOf(src)
	return strconv.FormatInt(v.Int(), 10), nil
}

func (c *OptimizedConverter) stringToInt(src any, srcInfo *TypeInfo, dstType reflect.Type, opts *Options) (any, error) {
	s := src.(string)
	bitSize := int(dstType.Size() * 8)

	if val, err := strconv.ParseInt(s, 10, bitSize); err == nil {
		return reflect.ValueOf(val).Convert(dstType).Interface(), nil
	}

	return nil, newConvertError("string", dstType.Name(), src, "invalid format")
}

func (c *OptimizedConverter) stringToBool(src any, srcInfo *TypeInfo, dstType reflect.Type, opts *Options) (any, error) {
	s := strings.ToLower(src.(string))

	// 检查自定义真假值集合
	if opts.Boolean.TrueSet != nil {
		if _, ok := opts.Boolean.TrueSet[s]; ok {
			return true, nil
		}
	}
	if opts.Boolean.FalseSet != nil {
		if _, ok := opts.Boolean.FalseSet[s]; ok {
			return false, nil
		}
	}

	// 使用标准解析
	if val, err := strconv.ParseBool(s); err == nil {
		return val, nil
	}

	return false, newConvertError("string", "bool", src, "invalid format")
}

func (c *OptimizedConverter) genericConvert(src any, srcInfo *TypeInfo, dstType reflect.Type, opts *Options) (any, error) {
	// 通用转换逻辑，处理复杂情况
	srcVal := reflect.ValueOf(src)
	dstInfo := getTypeInfo(dstType)

	// 数值类型之间的转换
	if (srcInfo.IsInt || srcInfo.IsUint || srcInfo.IsFloat) &&
		(dstInfo.IsInt || dstInfo.IsUint || dstInfo.IsFloat) {
		return c.convertNumeric(srcVal, dstType, opts)
	}

	// 字符串相关转换
	if srcInfo.IsString || srcInfo.IsBytes || srcInfo.IsRunes {
		return c.convertFromTextual(srcVal, dstType, opts)
	}

	if dstInfo.IsString || dstInfo.IsBytes || dstInfo.IsRunes {
		return c.convertToTextual(srcVal, dstType, opts)
	}

	return nil, newConvertError(srcVal.Type().Name(), dstType.Name(), src, "unsupported conversion")
}

func (c *OptimizedConverter) convertNumeric(srcVal reflect.Value, dstType reflect.Type, opts *Options) (any, error) {
	// 检查溢出
	if opts.Numeric.Strict {
		if err := c.checkNumericOverflow(srcVal, dstType); err != nil {
			return nil, err
		}
	}

	return srcVal.Convert(dstType).Interface(), nil
}

func (c *OptimizedConverter) checkNumericOverflow(srcVal reflect.Value, dstType reflect.Type) error {
	// 实现溢出检查逻辑
	// 这里简化实现，实际应该根据具体类型进行精确检查
	return nil
}

func (c *OptimizedConverter) convertFromTextual(srcVal reflect.Value, dstType reflect.Type, opts *Options) (any, error) {
	var str string

	switch srcVal.Kind() {
	case reflect.String:
		str = srcVal.String()
	case reflect.Slice:
		if srcVal.Type().Elem().Kind() == reflect.Uint8 {
			// []byte to string - 零拷贝
			str = bytesToString(srcVal.Bytes())
		} else if srcVal.Type().Elem().Kind() == reflect.Int32 {
			// []rune to string
			str = runesToString(srcVal.Interface().([]rune))
		}
	}

	return c.parseFromString(str, dstType, opts)
}

func (c *OptimizedConverter) convertToTextual(srcVal reflect.Value, dstType reflect.Type, opts *Options) (any, error) {
	str := c.formatToString(srcVal)

	switch dstType.Kind() {
	case reflect.String:
		return str, nil
	case reflect.Slice:
		if dstType.Elem().Kind() == reflect.Uint8 {
			// string to []byte - 零拷贝
			return stringToBytes(str), nil
		} else if dstType.Elem().Kind() == reflect.Int32 {
			// string to []rune
			return []rune(str), nil
		}
	}

	return nil, newConvertError(srcVal.Type().Name(), dstType.Name(), srcVal.Interface(), "unsupported textual conversion")
}

func (c *OptimizedConverter) parseFromString(str string, dstType reflect.Type, opts *Options) (any, error) {
	switch dstType.Kind() {
	case reflect.Bool:
		return c.parseBool(str, opts)
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return c.parseInt(str, dstType)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return c.parseUint(str, dstType)
	case reflect.Float32, reflect.Float64:
		return c.parseFloat(str, dstType)
	case reflect.Complex64, reflect.Complex128:
		return c.parseComplex(str, dstType)
	}

	return nil, newConvertError("string", dstType.Name(), str, "unsupported target type")
}

func (c *OptimizedConverter) formatToString(srcVal reflect.Value) string {
	switch srcVal.Kind() {
	case reflect.Bool:
		if srcVal.Bool() {
			return "true"
		}
		return "false"
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return strconv.FormatInt(srcVal.Int(), 10)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return strconv.FormatUint(srcVal.Uint(), 10)
	case reflect.Float32, reflect.Float64:
		bitSize := 32
		if srcVal.Kind() == reflect.Float64 {
			bitSize = 64
		}
		return strconv.FormatFloat(srcVal.Float(), 'f', -1, bitSize)
	case reflect.Complex64, reflect.Complex128:
		bitSize := 64
		if srcVal.Kind() == reflect.Complex128 {
			bitSize = 128
		}
		return strconv.FormatComplex(srcVal.Complex(), 'g', -1, bitSize)
	default:
		return srcVal.String()
	}
}

func (c *OptimizedConverter) parseBool(str string, opts *Options) (bool, error) {
	str = strings.ToLower(str)

	if opts.Boolean.TrueSet != nil {
		if _, ok := opts.Boolean.TrueSet[str]; ok {
			return true, nil
		}
	}
	if opts.Boolean.FalseSet != nil {
		if _, ok := opts.Boolean.FalseSet[str]; ok {
			return false, nil
		}
	}

	return strconv.ParseBool(str)
}

func (c *OptimizedConverter) parseInt(str string, dstType reflect.Type) (any, error) {
	bitSize := int(dstType.Size() * 8)
	val, err := strconv.ParseInt(str, 10, bitSize)
	if err != nil {
		return nil, err
	}
	return reflect.ValueOf(val).Convert(dstType).Interface(), nil
}

func (c *OptimizedConverter) parseUint(str string, dstType reflect.Type) (any, error) {
	bitSize := int(dstType.Size() * 8)
	val, err := strconv.ParseUint(str, 10, bitSize)
	if err != nil {
		return nil, err
	}
	return reflect.ValueOf(val).Convert(dstType).Interface(), nil
}

func (c *OptimizedConverter) parseFloat(str string, dstType reflect.Type) (any, error) {
	bitSize := int(dstType.Size() * 8)
	val, err := strconv.ParseFloat(str, bitSize)
	if err != nil {
		return nil, err
	}
	return reflect.ValueOf(val).Convert(dstType).Interface(), nil
}

func (c *OptimizedConverter) parseComplex(str string, dstType reflect.Type) (any, error) {
	bitSize := int(dstType.Size() * 8)
	val, err := strconv.ParseComplex(str, bitSize)
	if err != nil {
		return nil, err
	}
	return reflect.ValueOf(val).Convert(dstType).Interface(), nil
}

// ================================ 辅助函数 ================================

func getTypeName(info *TypeInfo) string {
	switch {
	case info.IsBool:
		return "bool"
	case info.IsInt:
		return "int"
	case info.IsUint:
		return "uint"
	case info.IsFloat:
		return "float"
	case info.IsString:
		return "string"
	case info.IsBytes:
		return "bytes"
	case info.IsRunes:
		return "runes"
	default:
		return "unknown"
	}
}
